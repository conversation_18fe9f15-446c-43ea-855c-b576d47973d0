# CPU版本依赖文件
# 适用于没有GPU或只使用CPU的环境

# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.11.7
pydantic-settings==2.1.0

# LangChain and AI
langchain==0.1.0
langchain-community==0.0.10
langchain-core==0.1.0

# LlamaIndex and AI
llama-index==0.10.11
llama-index-core==0.10.68.post1
llama-index-embeddings-huggingface==0.2.3
llama-index-llms-huggingface==0.2.8
llama-index-vector-stores-faiss==0.1.2
llama-index-vector-stores-milvus==0.1.23
llama-index-retrievers-bm25==0.1.3
llama-index-postprocessor-rankgpt-rerank==0.1.3
llama-index-postprocessor-cohere-rerank==0.1.7

# AI Models - CPU版本
transformers==4.40.2
torch==2.2.2  # CPU版本
sentence-transformers==3.1.1

# Vector Database and Search - CPU版本
faiss-cpu==1.7.4  # CPU版本
pymilvus==2.5.14  # GPU/CPU通用
numpy==1.26.4

# Database
psycopg2-binary==2.9.9
sqlalchemy==2.0.42
alembic==1.13.1

# Cache
redis==5.0.1
hiredis==2.2.3

# Document Processing
pypdf==4.3.1
python-docx==1.1.0
python-multipart==0.0.6

# Utilities
python-dotenv==1.1.1
loguru==0.7.2
httpx==0.28.1
aiofiles==23.2.1

# Development and Testing
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0
flake8==6.1.0

# Monitoring
prometheus-client==0.19.0

# Additional dependencies
beautifulsoup4==4.13.4
scikit-learn==1.7.1
pandas==2.3.1
nltk==3.9.1
