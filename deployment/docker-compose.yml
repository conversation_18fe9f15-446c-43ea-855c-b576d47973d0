# Docker Compose 版本声明
version: '3.8'

services:
  # PostgreSQL 数据库服务
  # 用于存储法律文档、用户查询记录、系统配置等结构化数据
  postgres:
    image: postgres:15                    # 使用 PostgreSQL 15 版本镜像
    container_name: law-gpt-postgres      # 容器名称，便于管理
    restart: unless-stopped               # 容器重启策略：除非手动停止，否则自动重启
    environment:
      POSTGRES_DB: law_gpt               # 数据库名称
      POSTGRES_USER: law_user            # 数据库用户名
      POSTGRES_PASSWORD: law_password    # 数据库密码（生产环境建议使用更强密码）
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --lc-collate=C --lc-ctype=C"  # 初始化参数
    ports:
      - "5432:5432"                      # 端口映射：主机5432端口映射到容器5432端口
    volumes:
      - postgres_data:/var/lib/postgresql/data              # 数据持久化存储
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql    # 数据库初始化脚本
    networks:
      - law-gpt-network                  # 加入自定义网络
    healthcheck:                         # 健康检查配置
      test: ["CMD-SHELL", "pg_isready -U law_user -d law_gpt"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis 缓存服务
  # 用于缓存查询结果、会话状态、临时数据等，提升系统响应速度
  redis:
    image: redis:7-alpine               # 使用轻量级的 Redis 7 Alpine 镜像
    container_name: law-gpt-redis       # 容器名称
    restart: unless-stopped             # 容器重启策略
    ports:
      - "6379:6379"                     # 端口映射：主机6379端口映射到容器6379端口
    volumes:
      - redis_data:/data                # 数据持久化存储
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    # 启动命令配置：
    # --appendonly yes: 启用AOF持久化
    # --maxmemory 512mb: 设置最大内存使用量为512MB
    # --maxmemory-policy allkeys-lru: 内存不足时使用LRU策略清理数据
    networks:
      - law-gpt-network                 # 加入自定义网络
    healthcheck:                        # 健康检查配置
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 法律问答系统主应用
  # 基于 FastAPI 的 Web 服务，提供 API 接口和前端界面
  law-gpt-app:
    build:
      context: ..                       # 构建上下文为项目根目录
      dockerfile: deployment/Dockerfile # Dockerfile 路径
    container_name: law-gpt-app         # 容器名称
    restart: unless-stopped             # 容器重启策略
    ports:
      - "8000:8000"                     # 端口映射：主机8000端口映射到容器8000端口
    environment:
      # 数据库连接配置
      - DATABASE_URL=************************************************/law_gpt
      # Redis 连接配置
      - REDIS_URL=redis://redis:6379/0
      # AI 模型路径配置
      - QWEN_MODEL_PATH=/app/models
      # Python 环境配置
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    volumes:
      # 应用代码挂载（开发模式，生产环境可移除）
      - ../app:/app/app
      # 数据目录挂载：存储上传的文档、向量索引等
      - ../data:/app/data
      # 模型目录挂载：存储 AI 模型文件
      - ../models:/app/models
      # 日志目录挂载
      - ../logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy      # 等待 PostgreSQL 健康检查通过
      redis:
        condition: service_healthy      # 等待 Redis 健康检查通过
    networks:
      - law-gpt-network                 # 加入自定义网络
    healthcheck:                        # 应用健康检查
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s                 # 启动后60秒开始健康检查

# 数据卷定义
# 用于持久化存储数据库和缓存数据
volumes:
  postgres_data:                        # PostgreSQL 数据存储卷
    driver: local
  redis_data:                           # Redis 数据存储卷
    driver: local

# 网络定义
# 创建自定义网络，确保服务间可以通过服务名相互访问
networks:
  law-gpt-network:
    driver: bridge                      # 使用桥接网络驱动
    ipam:
      config:
        - subnet: **********/16         # 自定义子网，避免网络冲突
