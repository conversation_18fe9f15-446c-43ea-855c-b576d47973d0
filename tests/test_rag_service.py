"""
RAG服务测试
"""
import pytest
import asyncio
from unittest.mock import Mock, patch
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.models.database import Base, Document, DocumentChunk
from app.models.schemas import QueryRequest
from app.services.rag_service import rag_service


@pytest.fixture
def test_db():
    """测试数据库"""
    engine = create_engine("sqlite:///:memory:")
    Base.metadata.create_all(engine)
    SessionLocal = sessionmaker(bind=engine)
    return SessionLocal()


@pytest.fixture
def sample_documents(test_db):
    """示例文档数据"""
    doc1 = Document(
        title="民法典第一条",
        content="为了保护民事主体的合法权益，调整民事关系，维护社会和经济秩序，适应中国特色社会主义发展要求，弘扬社会主义核心价值观，根据宪法，制定本法。",
        category="民法",
        source="《中华人民共和国民法典》"
    )
    
    doc2 = Document(
        title="合同法基本原则",
        content="当事人订立、履行合同，应当遵守法律、行政法规，尊重社会公德，不得扰乱社会经济秩序，损害社会公共利益。",
        category="民法",
        source="《中华人民共和国民法典》"
    )
    
    test_db.add(doc1)
    test_db.add(doc2)
    test_db.commit()
    
    return [doc1, doc2]


@pytest.mark.asyncio
async def test_query_processing(test_db, sample_documents):
    """测试查询处理"""
    
    # Mock嵌入服务
    with patch('app.services.rag_service.embedding_service') as mock_embedding:
        mock_embedding.encode_text.return_value = [0.1] * 768
        
        # Mock向量存储
        with patch('app.services.rag_service.vector_store') as mock_vector_store:
            mock_vector_store.search.return_value = [
                (str(sample_documents[0].id), 0.9, {'document_id': str(sample_documents[0].id)})
            ]
            
            # Mock LLM服务
            with patch('app.services.rag_service.llm_service') as mock_llm:
                mock_llm.generate_legal_answer.return_value = "根据民法典第一条的规定..."
                
                # 创建查询请求
                request = QueryRequest(
                    question="民法典的立法目的是什么？",
                    user_id="test_user"
                )
                
                # 执行查询
                response = await rag_service.query(request, test_db)
                
                # 验证结果
                assert response.answer == "根据民法典第一条的规定..."
                assert response.confidence_score > 0
                assert len(response.retrieved_documents) > 0


@pytest.mark.asyncio
async def test_empty_query_handling():
    """测试空查询处理"""
    
    with patch('app.services.rag_service.vector_store') as mock_vector_store:
        mock_vector_store.search.return_value = []
        
        request = QueryRequest(question="不存在的法律问题")
        
        # 这里需要一个测试数据库会话
        test_db = Mock()
        
        response = await rag_service.query(request, test_db)
        
        # 验证空结果处理
        assert "没有找到相关的法律文档" in response.answer
        assert response.confidence_score == 0.0


def test_confidence_calculation():
    """测试置信度计算"""
    
    # 测试有文档的情况
    documents = [
        {'score': 0.9},
        {'score': 0.8},
        {'score': 0.7}
    ]
    
    confidence = rag_service._calculate_confidence(documents)
    assert 0 <= confidence <= 1
    
    # 测试无文档的情况
    empty_docs = []
    confidence_empty = rag_service._calculate_confidence(empty_docs)
    assert confidence_empty == 0.0


def test_context_limiting():
    """测试上下文长度限制"""
    
    # 创建超长文档
    long_documents = [
        {'content': 'A' * 2000, 'title': 'Doc1'},
        {'content': 'B' * 2000, 'title': 'Doc2'},
        {'content': 'C' * 2000, 'title': 'Doc3'}
    ]
    
    # 设置较小的上下文限制进行测试
    original_max_length = rag_service.max_context_length
    rag_service.max_context_length = 3000
    
    try:
        limited_docs = rag_service._limit_context(long_documents)
        
        # 验证总长度不超过限制
        total_length = sum(len(doc['content']) for doc in limited_docs)
        assert total_length <= 3000
        
    finally:
        # 恢复原始设置
        rag_service.max_context_length = original_max_length


if __name__ == "__main__":
    pytest.main([__file__])
