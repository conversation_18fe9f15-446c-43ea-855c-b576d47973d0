"""
大语言模型服务
基于Qwen3-8B模型
"""
import os
import torch
from typing import List, Dict, Any, Optional
from transformers import AutoTokenizer, AutoModelForCausalLM, GenerationConfig
from langchain.llms.base import LLM
from langchain.callbacks.manager import Callback<PERSON>anagerF<PERSON><PERSON>MRun
from loguru import logger
from app.config import settings, ModelConfig, PromptTemplates


class QwenLLM(LLM):
    """自定义Qwen LLM包装器"""
    
    model: Any = None
    tokenizer: Any = None
    device: str = "auto"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._initialize_model()
    
    def _initialize_model(self):
        """初始化Qwen模型"""
        try:
            model_name = ModelConfig.QWEN_CONFIG["model_name"]
            
            # 如果本地有模型文件，使用本地路径
            if settings.qwen_model_path and os.path.exists(settings.qwen_model_path):
                model_path = settings.qwen_model_path
            else:
                model_path = model_name
            
            # 加载tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                model_path,
                trust_remote_code=True
            )
            
            # 加载模型
            self.model = AutoModelForCausalLM.from_pretrained(
                model_path,
                device_map=ModelConfig.QWEN_CONFIG["device_map"],
                torch_dtype=getattr(torch, ModelConfig.QWEN_CONFIG["torch_dtype"]),
                trust_remote_code=ModelConfig.QWEN_CONFIG["trust_remote_code"]
            )
            
            # 设置生成配置
            self.generation_config = GenerationConfig(
                max_new_tokens=settings.max_tokens,
                temperature=settings.temperature,
                do_sample=True,
                top_p=0.8,
                top_k=50,
                repetition_penalty=1.1,
                pad_token_id=self.tokenizer.eos_token_id
            )
            
            logger.info(f"Qwen模型加载成功: {model_path}")
            
        except Exception as e:
            logger.error(f"Qwen模型加载失败: {e}")
            raise
    
    @property
    def _llm_type(self) -> str:
        return "qwen"
    
    def _call(
        self,
        prompt: str,
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> str:
        """调用模型生成回答"""
        try:
            # 编码输入
            inputs = self.tokenizer(prompt, return_tensors="pt")
            
            # 移动到正确的设备
            if hasattr(self.model, 'device'):
                inputs = {k: v.to(self.model.device) for k, v in inputs.items()}
            
            # 生成回答
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    generation_config=self.generation_config,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            # 解码输出
            response = self.tokenizer.decode(
                outputs[0][inputs['input_ids'].shape[1]:], 
                skip_special_tokens=True
            )
            
            # 处理停止词
            if stop:
                for stop_word in stop:
                    if stop_word in response:
                        response = response.split(stop_word)[0]
            
            return response.strip()
            
        except Exception as e:
            logger.error(f"模型生成失败: {e}")
            return "抱歉，生成回答时出现错误。"


class LLMService:
    """大语言模型服务"""
    
    def __init__(self):
        self.llm = QwenLLM()
    
    def generate_legal_answer(self, 
                            question: str, 
                            context_documents: List[Dict[str, Any]]) -> str:
        """
        生成法律问题的回答
        
        Args:
            question: 用户问题
            context_documents: 检索到的相关文档
            
        Returns:
            生成的回答
        """
        try:
            # 构建上下文
            context = self._build_context(context_documents)
            
            # 构建提示词
            prompt = PromptTemplates.LEGAL_QA_TEMPLATE.format(
                context=context,
                question=question
            )
            
            # 生成回答
            answer = self.llm(prompt)
            
            return answer
            
        except Exception as e:
            logger.error(f"生成法律回答失败: {e}")
            return "抱歉，无法生成回答。请稍后重试。"
    
    def _build_context(self, documents: List[Dict[str, Any]]) -> str:
        """构建上下文字符串"""
        context_parts = []
        
        for i, doc in enumerate(documents, 1):
            title = doc.get('title', '未知文档')
            content = doc.get('content', '')
            source = doc.get('source', '')
            
            context_part = f"""
文档 {i}: {title}
来源: {source}
内容: {content}
---
"""
            context_parts.append(context_part)
        
        return "\n".join(context_parts)
    
    def generate_document_summary(self, content: str) -> str:
        """
        生成文档摘要
        
        Args:
            content: 文档内容
            
        Returns:
            文档摘要
        """
        try:
            prompt = PromptTemplates.DOCUMENT_SUMMARY_TEMPLATE.format(
                content=content[:2000]  # 限制输入长度
            )
            
            summary = self.llm(prompt)
            return summary
            
        except Exception as e:
            logger.error(f"生成文档摘要失败: {e}")
            return "无法生成摘要"
    
    def extract_legal_entities(self, text: str) -> List[str]:
        """
        提取法律实体（法条、案例等）
        
        Args:
            text: 输入文本
            
        Returns:
            提取的法律实体列表
        """
        try:
            prompt = f"""
请从以下文本中提取法律相关的实体，包括：
1. 法律条文
2. 案例名称
3. 法律概念
4. 相关法规

文本：{text}

请以列表形式返回，每行一个实体：
"""
            
            result = self.llm(prompt)
            
            # 解析结果
            entities = []
            for line in result.split('\n'):
                line = line.strip()
                if line and not line.startswith('请') and not line.startswith('文本'):
                    # 移除序号和特殊字符
                    entity = line.lstrip('0123456789.- ').strip()
                    if entity:
                        entities.append(entity)
            
            return entities
            
        except Exception as e:
            logger.error(f"提取法律实体失败: {e}")
            return []


# 全局LLM服务实例
llm_service = LLMService()
